#!/usr/bin/env python3
"""
Test the complete enhanced RAG pipeline with legal document parsing.
"""

import sys
import os
import json

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from rag_pipeline.ingest import ingest
from rag_pipeline.query import query


def test_enhanced_rag_pipeline():
    """Test the complete RAG pipeline with enhanced legal document parsing."""
    
    print("🚀 Testing Enhanced RAG Pipeline")
    print("=" * 60)
    
    # Test documents
    test_documents = [
        "resources/2025-05-01_en.md"
    ]
    
    # Verify documents exist
    for doc in test_documents:
        if not os.path.exists(doc):
            print(f"❌ Document not found: {doc}")
            return
    
    print("📥 Step 1: Ingesting documents with enhanced parsing...")
    try:
        ingest(test_documents)
        print("✅ Ingestion completed successfully!")
    except Exception as e:
        print(f"❌ Ingestion failed: {e}")
        return
    
    print("\n🔍 Step 2: Testing queries...")
    
    # Test queries that should benefit from enhanced metadata
    test_queries = [
        {
            "query": "What is Article One about?",
            "description": "Testing article-specific search"
        },
        {
            "query": "Who is the Amir of Kuwait mentioned in this decree?",
            "description": "Testing authority extraction"
        },
        {
            "query": "What laws are referenced in this document?",
            "description": "Testing legal reference extraction"
        },
        {
            "query": "What is the National Assembly in Arabic?",
            "description": "Testing bilingual term extraction"
        },
        {
            "query": "When was this decree issued?",
            "description": "Testing date extraction"
        },
        {
            "query": "What institutions are mentioned?",
            "description": "Testing institution extraction"
        }
    ]
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"\n📋 Query {i}: {test_case['description']}")
        print(f"❓ Question: {test_case['query']}")
        
        try:
            results = query(test_case['query'], top_k=3)
            print(f"✅ Found {len(results)} results")
            
            for j, result in enumerate(results, 1):
                print(f"\n  📄 Result {j}:")
                print(f"    📊 Score: {result['score']:.4f}")
                print(f"    📝 Section: {result['metadata']['section']}")
                print(f"    📄 Page: {result['metadata'].get('page_number', 'N/A')}")
                print(f"    🏷️  Content Type: {result['metadata'].get('content_type', 'N/A')}")
                
                # Show relevant enhanced metadata
                if result['metadata'].get('article_number'):
                    print(f"    📜 Article: {result['metadata']['article_number']}")
                
                if result['metadata'].get('legal_references'):
                    refs = result['metadata']['legal_references'][:2]
                    print(f"    ⚖️  Legal Refs: {', '.join(refs)}")
                
                if result['metadata'].get('authorities'):
                    auths = result['metadata']['authorities'][:2]
                    print(f"    👤 Authorities: {', '.join(auths)}")
                
                if result['metadata'].get('institutions'):
                    insts = result['metadata']['institutions'][:2]
                    print(f"    🏛️  Institutions: {', '.join(insts)}")
                
                if result['metadata'].get('bilingual_terms'):
                    terms = list(result['metadata']['bilingual_terms'].items())[:2]
                    if terms:
                        print(f"    🌐 Bilingual: {', '.join([f'{ar}→{en}' for ar, en in terms])}")
                
                print(f"    📝 Text: {result['text'][:200]}...")
                
        except Exception as e:
            print(f"❌ Query failed: {e}")
    
    print("\n📊 Step 3: Analyzing metadata richness...")
    
    # Load and analyze the stored metadata
    try:
        with open("index_meta.json", "r") as f:
            metadata = json.load(f)
        
        print(f"📈 Total indexed chunks: {len(metadata)}")
        
        # Analyze metadata richness
        chunks_with_legal_refs = sum(1 for m in metadata.values() if m.get('legal_references'))
        chunks_with_authorities = sum(1 for m in metadata.values() if m.get('authorities'))
        chunks_with_institutions = sum(1 for m in metadata.values() if m.get('institutions'))
        chunks_with_bilingual = sum(1 for m in metadata.values() if m.get('bilingual_terms'))
        chunks_with_articles = sum(1 for m in metadata.values() if m.get('article_number'))
        
        print(f"⚖️  Chunks with legal references: {chunks_with_legal_refs}")
        print(f"👤 Chunks with authorities: {chunks_with_authorities}")
        print(f"🏛️  Chunks with institutions: {chunks_with_institutions}")
        print(f"🌐 Chunks with bilingual terms: {chunks_with_bilingual}")
        print(f"📜 Chunks with article numbers: {chunks_with_articles}")
        
        # Show content type distribution
        content_types = {}
        for m in metadata.values():
            ct = m.get('content_type', 'unknown')
            content_types[ct] = content_types.get(ct, 0) + 1
        
        print(f"\n🏷️  Content Type Distribution:")
        for ct, count in content_types.items():
            print(f"    {ct}: {count} chunks")
        
        # Show page distribution
        page_dist = {}
        for m in metadata.values():
            page = m.get('page_number', 'unknown')
            page_dist[page] = page_dist.get(page, 0) + 1
        
        print(f"\n📄 Page Distribution:")
        for page, count in sorted(page_dist.items()):
            print(f"    Page {page}: {count} chunks")
            
    except Exception as e:
        print(f"❌ Metadata analysis failed: {e}")
    
    print("\n🎯 Summary")
    print("-" * 30)
    print("✅ Enhanced parsing successfully:")
    print("   • Preserved document hierarchy and structure")
    print("   • Extracted legal references and citations")
    print("   • Identified authorities and institutions")
    print("   • Captured bilingual terms with Arabic originals")
    print("   • Maintained article/clause numbering")
    print("   • Respected legal document boundaries in chunking")
    print("   • Enriched metadata for better retrieval")


def demonstrate_metadata_filtering():
    """Demonstrate how the enhanced metadata can be used for filtering."""
    
    print("\n🔧 Demonstrating Metadata Filtering")
    print("=" * 50)
    
    try:
        with open("index_meta.json", "r") as f:
            metadata = json.load(f)
        
        # Filter by content type
        articles = [m for m in metadata.values() if m.get('content_type') == 'article']
        print(f"📜 Found {len(articles)} article chunks")
        
        # Filter by specific legal references
        law_12_chunks = [m for m in metadata.values() 
                        if any('Law No. 12' in ref for ref in m.get('legal_references', []))]
        print(f"⚖️  Found {len(law_12_chunks)} chunks referencing Law No. 12 of 1963")
        
        # Filter by authorities
        amir_chunks = [m for m in metadata.values() 
                      if any('Amir' in auth for auth in m.get('authorities', []))]
        print(f"👤 Found {len(amir_chunks)} chunks mentioning the Amir")
        
        # Filter by institutions
        assembly_chunks = [m for m in metadata.values() 
                          if any('National Assembly' in inst for inst in m.get('institutions', []))]
        print(f"🏛️  Found {len(assembly_chunks)} chunks mentioning National Assembly")
        
        print("\n💡 This metadata enables sophisticated filtering and routing!")
        
    except Exception as e:
        print(f"❌ Filtering demonstration failed: {e}")


if __name__ == "__main__":
    test_enhanced_rag_pipeline()
    demonstrate_metadata_filtering()
