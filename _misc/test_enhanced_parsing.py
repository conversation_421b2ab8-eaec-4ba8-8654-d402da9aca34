#!/usr/bin/env python3
"""
Test script to demonstrate the enhanced legal document parsing capabilities.
"""

import json
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from rag_pipeline.utils import parse_markdown_to_sections, chunk_section


def test_enhanced_parsing():
    """Test the enhanced parsing on the legal document."""
    
    print("🔍 Testing Enhanced Legal Document Parsing")
    print("=" * 60)
    
    # Parse the legal document
    document_path = "resources/2025-05-01_en.md"
    
    if not os.path.exists(document_path):
        print(f"❌ Document not found: {document_path}")
        return
    
    print(f"📄 Parsing document: {document_path}")
    sections = parse_markdown_to_sections(document_path)
    
    print(f"✅ Found {len(sections)} sections")
    print()
    
    # Display summary of parsed sections
    print("📋 Section Summary:")
    print("-" * 40)
    
    for i, section in enumerate(sections[:10]):  # Show first 10 sections
        print(f"Section {i+1}:")
        print(f"  📝 Heading: {section['heading']}")
        print(f"  📊 Level: {section['level']}")
        print(f"  🏗️  Hierarchy: {' > '.join(section['hierarchy'])}")
        print(f"  📄 Page: {section['page_number']}")
        print(f"  🏷️  Content Type: {section['content_type']}")
        
        if section['article_number']:
            print(f"  📜 Article: {section['article_number']}")
        
        if section['legal_references']:
            print(f"  ⚖️  Legal Refs: {', '.join(section['legal_references'][:3])}")
        
        if section['authorities']:
            print(f"  👤 Authorities: {', '.join(section['authorities'][:2])}")
        
        if section['institutions']:
            print(f"  🏛️  Institutions: {', '.join(section['institutions'][:2])}")
        
        if section['bilingual_terms']:
            terms = list(section['bilingual_terms'].items())[:2]
            print(f"  🌐 Bilingual: {', '.join([f'{ar}→{en}' for ar, en in terms])}")
        
        print(f"  📝 Text Preview: {section['text'][:100]}...")
        print()
    
    if len(sections) > 10:
        print(f"... and {len(sections) - 10} more sections")
        print()
    
    # Test chunking on a specific section
    print("🔧 Testing Enhanced Chunking")
    print("-" * 40)
    
    # Find a section with substantial content
    test_section = None
    for section in sections:
        if len(section['text']) > 500 and section['content_type'] == 'article':
            test_section = section
            break
    
    if test_section:
        print(f"📝 Testing chunking on: {test_section['heading']}")
        chunks = chunk_section(test_section['text'], max_tokens=150, overlap_tokens=30)
        
        print(f"✅ Created {len(chunks)} chunks")
        
        for i, chunk in enumerate(chunks):
            print(f"  Chunk {i+1}:")
            print(f"    🏷️  Type: {chunk['boundary_type']}")
            print(f"    📊 Tokens: ~{len(chunk['text'].split())}")
            print(f"    📝 Text: {chunk['text'][:100]}...")
            print()
    
    # Show metadata richness
    print("📊 Metadata Analysis")
    print("-" * 40)
    
    total_legal_refs = sum(len(s['legal_references']) for s in sections)
    total_authorities = sum(len(s['authorities']) for s in sections)
    total_institutions = sum(len(s['institutions']) for s in sections)
    total_bilingual = sum(len(s['bilingual_terms']) for s in sections)
    
    print(f"⚖️  Total Legal References: {total_legal_refs}")
    print(f"👤 Total Authorities: {total_authorities}")
    print(f"🏛️  Total Institutions: {total_institutions}")
    print(f"🌐 Total Bilingual Terms: {total_bilingual}")
    
    # Show unique values
    all_refs = set()
    all_auths = set()
    all_insts = set()
    
    for section in sections:
        all_refs.update(section['legal_references'])
        all_auths.update(section['authorities'])
        all_insts.update(section['institutions'])
    
    print(f"📚 Unique Legal References: {len(all_refs)}")
    print(f"👥 Unique Authorities: {len(all_auths)}")
    print(f"🏢 Unique Institutions: {len(all_insts)}")
    
    print()
    print("🎯 Sample Extracted Data:")
    print("-" * 30)
    
    if all_refs:
        print(f"⚖️  Legal Refs: {list(all_refs)[:3]}")
    if all_auths:
        print(f"👤 Authorities: {list(all_auths)[:3]}")
    if all_insts:
        print(f"🏛️  Institutions: {list(all_insts)[:3]}")


def save_sample_metadata():
    """Save a sample of the enhanced metadata to JSON for inspection."""
    
    document_path = "resources/2025-05-01_en.md"
    if not os.path.exists(document_path):
        return
    
    sections = parse_markdown_to_sections(document_path)
    
    # Save first few sections with full metadata
    sample_data = {
        "document_summary": {
            "total_sections": len(sections),
            "content_types": list(set(s['content_type'] for s in sections)),
            "page_count": max(s['page_number'] for s in sections if s['page_number']),
        },
        "sample_sections": sections[:3]  # First 3 sections with full metadata
    }
    
    with open("sample_enhanced_metadata.json", "w", encoding="utf-8") as f:
        json.dump(sample_data, f, indent=2, ensure_ascii=False)
    
    print("💾 Saved sample metadata to: sample_enhanced_metadata.json")


if __name__ == "__main__":
    test_enhanced_parsing()
    save_sample_metadata()
