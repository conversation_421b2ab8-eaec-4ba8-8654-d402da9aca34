#!/usr/bin/env python3
"""
Demonstration of advanced filtering capabilities with enhanced legal document parsing.
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from rag_pipeline.query import query, query_with_filters


def demo_advanced_filtering():
    """Demonstrate advanced filtering capabilities."""
    
    print("🎯 Advanced Filtering Demonstration")
    print("=" * 60)
    
    # Test 1: Filter by content type
    print("\n📜 Test 1: Filter by Article Content Type")
    print("-" * 50)
    
    results = query_with_filters(
        "What are the main provisions?",
        top_k=5,
        filters={"content_type": "article"}
    )
    
    print(f"Found {len(results)} article-specific results:")
    for i, result in enumerate(results, 1):
        meta = result["metadata"]
        print(f"  {i}. {meta['section']} (Article: {meta.get('article_number', 'N/A')})")
        print(f"     Score: {result['score']:.4f}")
        print(f"     Text: {result['text'][:100]}...")
        print()
    
    # Test 2: Filter by specific legal reference
    print("\n⚖️  Test 2: Filter by Specific Legal Reference")
    print("-" * 50)
    
    results = query_with_filters(
        "What does the law say about civil service?",
        top_k=3,
        filters={"legal_references": ["Law No. 12 of 1963"]}
    )
    
    print(f"Found {len(results)} results referencing Law No. 12 of 1963:")
    for i, result in enumerate(results, 1):
        meta = result["metadata"]
        refs = meta.get('legal_references', [])
        relevant_refs = [ref for ref in refs if 'Law No. 12' in ref]
        print(f"  {i}. {meta['section']}")
        print(f"     Legal Refs: {', '.join(relevant_refs)}")
        print(f"     Score: {result['score']:.4f}")
        print()
    
    # Test 3: Filter by authority
    print("\n👤 Test 3: Filter by Authority")
    print("-" * 50)
    
    results = query_with_filters(
        "Who has authority in this matter?",
        top_k=3,
        filters={"authorities": ["Prime Minister"]}
    )
    
    print(f"Found {len(results)} results mentioning Prime Minister:")
    for i, result in enumerate(results, 1):
        meta = result["metadata"]
        auths = meta.get('authorities', [])
        relevant_auths = [auth for auth in auths if 'Prime Minister' in auth]
        print(f"  {i}. {meta['section']}")
        print(f"     Authorities: {', '.join(relevant_auths[:2])}")
        print(f"     Score: {result['score']:.4f}")
        print()
    
    # Test 4: Filter by institution
    print("\n🏛️  Test 4: Filter by Institution")
    print("-" * 50)
    
    results = query_with_filters(
        "What about the National Assembly?",
        top_k=3,
        filters={"institutions": ["National Assembly"]}
    )
    
    print(f"Found {len(results)} results mentioning National Assembly:")
    for i, result in enumerate(results, 1):
        meta = result["metadata"]
        insts = meta.get('institutions', [])
        relevant_insts = [inst for inst in insts if 'National Assembly' in inst]
        print(f"  {i}. {meta['section']}")
        print(f"     Institutions: {', '.join(relevant_insts)}")
        print(f"     Score: {result['score']:.4f}")
        print()
    
    # Test 5: Multiple filters combined
    print("\n🔗 Test 5: Combined Filters")
    print("-" * 50)
    
    results = query_with_filters(
        "What are the article provisions about the National Assembly?",
        top_k=3,
        filters={
            "content_type": "article",
            "institutions": ["National Assembly"]
        }
    )
    
    print(f"Found {len(results)} article results about National Assembly:")
    for i, result in enumerate(results, 1):
        meta = result["metadata"]
        print(f"  {i}. {meta['section']} (Article: {meta.get('article_number', 'N/A')})")
        print(f"     Content Type: {meta.get('content_type')}")
        print(f"     Institutions: {', '.join(meta.get('institutions', []))}")
        print(f"     Score: {result['score']:.4f}")
        print()
    
    # Test 6: Page-specific filtering
    print("\n📄 Test 6: Page-Specific Filtering")
    print("-" * 50)
    
    results = query_with_filters(
        "What's on page 2?",
        top_k=5,
        filters={"page_number": 2}
    )
    
    print(f"Found {len(results)} results from page 2:")
    for i, result in enumerate(results, 1):
        meta = result["metadata"]
        print(f"  {i}. {meta['section']}")
        print(f"     Page: {meta.get('page_number')}")
        print(f"     Content Type: {meta.get('content_type')}")
        print(f"     Score: {result['score']:.4f}")
        print()


def demo_bilingual_search():
    """Demonstrate bilingual search capabilities."""
    
    print("\n🌐 Bilingual Search Demonstration")
    print("=" * 50)
    
    # Search for Arabic terms
    arabic_queries = [
        "مجلس الأمة",  # National Assembly
        "أمير الكويت",  # Amir of Kuwait
        "رئيس مجلس الوزراء"  # Prime Minister
    ]
    
    for query_text in arabic_queries:
        print(f"\n🔍 Searching for: {query_text}")
        results = query(query_text, top_k=2)
        
        for i, result in enumerate(results, 1):
            meta = result["metadata"]
            bilingual = meta.get('bilingual_terms', {})
            
            # Find relevant bilingual terms
            relevant_terms = {}
            for arabic, english in bilingual.items():
                if query_text in arabic or any(word in arabic for word in query_text.split()):
                    relevant_terms[arabic] = english
            
            print(f"  Result {i}: {meta['section']}")
            print(f"    Score: {result['score']:.4f}")
            if relevant_terms:
                print(f"    Bilingual matches: {list(relevant_terms.items())[:2]}")
            print(f"    Text: {result['text'][:100]}...")
            print()


def demo_hierarchical_search():
    """Demonstrate hierarchical document structure search."""
    
    print("\n🏗️  Hierarchical Structure Demonstration")
    print("=" * 50)
    
    # Get all results to analyze hierarchy
    results = query("decree law provisions", top_k=10)
    
    # Group by hierarchy levels
    hierarchy_groups = {}
    for result in results:
        meta = result["metadata"]
        hierarchy = meta.get('heading_hierarchy', [])
        level = meta.get('level', 0)
        
        if level not in hierarchy_groups:
            hierarchy_groups[level] = []
        hierarchy_groups[level].append((result, hierarchy))
    
    print("Document hierarchy structure:")
    for level in sorted(hierarchy_groups.keys()):
        print(f"\n📊 Level {level}:")
        for result, hierarchy in hierarchy_groups[level][:3]:  # Show top 3 per level
            meta = result["metadata"]
            hierarchy_path = " > ".join(hierarchy) if hierarchy else "Root"
            print(f"  • {meta['section']}")
            print(f"    Path: {hierarchy_path}")
            print(f"    Content Type: {meta.get('content_type')}")
            print(f"    Score: {result['score']:.4f}")


if __name__ == "__main__":
    demo_advanced_filtering()
    demo_bilingual_search()
    demo_hierarchical_search()
    
    print("\n🎉 Advanced Features Summary")
    print("=" * 50)
    print("✅ Content type filtering (article, clause, header)")
    print("✅ Legal reference filtering")
    print("✅ Authority and institution filtering")
    print("✅ Page-specific filtering")
    print("✅ Combined multi-filter queries")
    print("✅ Bilingual search capabilities")
    print("✅ Hierarchical document structure awareness")
    print("✅ Rich metadata extraction and utilization")
