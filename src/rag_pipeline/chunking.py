from typing import List, Dict, Any, Optional

def create_contextual_window(all_chunks: List[Dict[str, Any]], chunk_index: int, window_size: int = 1) -> str:
    if not (0 <= chunk_index < len(all_chunks)):
        raise ValueError("chunk_index is out of bounds.")
    start = max(0, chunk_index - window_size)
    end = min(len(all_chunks), chunk_index + window_size + 1)
    context_chunks = all_chunks[start:end]
    return " ".join([chunk['text'] for chunk in context_chunks])