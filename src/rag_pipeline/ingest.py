from typing import List, Dict, Any, Optional
import os
import json
from transformers import AutoTokenizer
import re
import requests
import networkx as nx

from .utils import parse_markdown
from .embedder import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bed<PERSON><PERSON><PERSON><PERSON>, Lance<PERSON><PERSON>anager, VectorIndexer

from .ollama import OllamaClient
from .overview import OverviewBuilder
from .chunking import create_contextual_window
from .latechunking import LateChunkEncoder
# from .graph import GraphExtractor

from huggingface_hub import login

SYSTEM_PROMPT = "You are an expert at summarizing and providing context for document sections based on their local surroundings."

LOCAL_CONTEXT_PROMPT_TEMPLATE = """<local_context>
{local_context_text}
</local_context>"""

CHUNK_PROMPT_TEMPLATE = """Here is the specific chunk we want to situate within the local context provided:
<chunk>
{chunk_content}
</chunk>

Based *only* on the local context provided, give a very short (2-5 sentence) context summary to situate this specific chunk. 
Focus on the chunk's topic and its relation to the immediately surrounding text shown in the local context. 
Focus on the the overall theme of the context, make sure to include topics, concepts, and other relevant information.
Answer *only* with the succinct context and nothing else."""



def generate_completion(model: str,prompt: str,*,format: str = "",enable_thinking: bool) -> Dict[str, Any]:
    try:
        payload = {
            "model": model,
            "prompt": prompt,
            "stream": False
        }
        if format:
            payload["format"] = format
        
        if enable_thinking is not None:
            payload["chat_template_kwargs"] = {"enable_thinking": enable_thinking}

        ollama_api_url = "http://localhost:11434/api"

        response = requests.post(
            f"{ollama_api_url}/generate",
            json=payload
        )
        response.raise_for_status()
        response_lines = response.text.strip().split('\n')
        final_response = json.loads(response_lines[-1])
        return final_response

    except requests.exceptions.RequestException as e:
        print(f"Error generating completion: {e}")
        return {}
        
def generate_summary(local_context_text: str, chunk_text: str) -> str:
    human_prompt_content = (
        f"{LOCAL_CONTEXT_PROMPT_TEMPLATE.format(local_context_text=local_context_text)}\n\n"
        f"{CHUNK_PROMPT_TEMPLATE.format(chunk_content=chunk_text)}"
    )

    llm_model = "qwen3:0.6b"
    try:
        full_prompt = f"{SYSTEM_PROMPT}\n\n{human_prompt_content}"
        
        response = generate_completion(llm_model, full_prompt, enable_thinking=False)
        summary_raw = response.get('response', '').strip()

        cleaned = re.sub(r'<think[^>]*>.*?</think>', '', summary_raw, flags=re.IGNORECASE | re.DOTALL)
        cleaned = re.sub(r'<assistant[^>]*>|</assistant>', '', cleaned, flags=re.IGNORECASE)
        if 'Answer:' in cleaned:
            cleaned = cleaned.split('Answer:', 1)[1]

        summary = next((ln.strip() for ln in cleaned.splitlines() if ln.strip()), '')

        if not summary:
            summary = summary_raw

        if not summary or len(summary) < 5:
            print("Generated context summary is too short or empty. Skipping enrichment for this chunk.")
            return ""
        
        return summary

    except Exception as e:
        print(f"LLM invocation failed during contextualization: {e}", exc_info=True)
        return ""

def token_len(text: str) -> int:
    tokenizer_model = "Qwen/Qwen3-Embedding-0.6B"
    try:
        # tokenizer = AutoTokenizer.from_pretrained(tokenizer_model, trust_remote_code=True,use_auth_token=True)
        tokenizer = AutoTokenizer.from_pretrained("/Users/<USER>/.cache/huggingface/hub/models--Qwen--Qwen3-Embedding-0.6B/snapshots/c54f2e6e80b2d7b7de06f51cec4959f6b3e03418",trust_remote_code=False)
    except Exception as e:
        print(f"Warning: Failed to load tokenizer {tokenizer_model}: {e}")
        print("Falling back to character-based approximation (4 chars ≈ 1 token)")
        
    return len(tokenizer.tokenize(text))

def split_text(text: str, separators: List[str]) -> List[str]:
    final_chunks = []
    chunks_to_process = [text]
    max_chunk_size = 1500
    
    for sep in separators:
        new_chunks = []
        for chunk in chunks_to_process:
            if token_len(chunk) > max_chunk_size:
                sub_chunks = re.split(f'({sep})', chunk)
                combined = []
                i = 0
                while i < len(sub_chunks):
                    if i + 1 < len(sub_chunks) and sub_chunks[i+1] == sep:
                        combined.append(sub_chunks[i+1] + sub_chunks[i+2])
                        i += 3
                    else:
                        if sub_chunks[i]:
                            combined.append(sub_chunks[i])
                        i += 1
                new_chunks.extend(combined)
            else:
                new_chunks.append(chunk)
        chunks_to_process = new_chunks
    
    final_chunks = []
    for chunk in chunks_to_process:
        if token_len(chunk) > max_chunk_size:
            words = chunk.split()
            current_chunk = ""
            for word in words:
                test_chunk = current_chunk + " " + word if current_chunk else word
                if token_len(test_chunk) <= max_chunk_size:
                    current_chunk = test_chunk
                else:
                    if current_chunk:
                        final_chunks.append(current_chunk)
                    current_chunk = word
            if current_chunk:
                final_chunks.append(current_chunk)
        else:
            final_chunks.append(chunk)

    return final_chunks
    
def chunk(text: str, document_id: str, document_metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    if not text:
        return []

    max_chunk_size=1500
    min_chunk_size = 200

    crop_text = text.replace("\n", "").replace("'", "")
    crop_text = crop_text[:100]
    print (f"chunk:{crop_text} full_len:{len(text)}")

    #//////

    from langchain_text_splitters import MarkdownHeaderTextSplitter
    from langchain_text_splitters import RecursiveCharacterTextSplitter

    headers_to_split_on = [
        ("#", "Header1"),
        ("##", "Header2"),
        ("###", "Header3"),
    ]
    markdown_splitter = MarkdownHeaderTextSplitter(
        headers_to_split_on=headers_to_split_on, strip_headers=False
    )
    md_header_splits = markdown_splitter.split_text(text)   

    chunk_size = 250
    chunk_overlap = 30
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=chunk_size, chunk_overlap=chunk_overlap
    )

    documents = text_splitter.split_documents(md_header_splits)
    # print (f"+++++++++++++++++++++++splits LEN:{len(documents)}")

    raw_chunks = []
    for doc in documents:
        header1 = doc.metadata.get("Header1", "")
        header2 = doc.metadata.get("Header2", "")
        header3 = doc.metadata.get("Header3", "")
        content = doc.page_content.strip()

        if header1 or header2:
            test_string = f"{header1} > {header2} > {header3}: {content}"
        else:
            test_string = content 

        test_string_tokenlen = token_len(test_string)
        # print (f"++++++++++++++++++++>>>test_string_tokenlen:{test_string_tokenlen}")
        raw_chunks.append(test_string)    

    # print (f"==========>>raw_chunks LEN:{len(raw_chunks)}")
    # print (f"{raw_chunks}")
    # print (30*"-")
    
    merged_chunks_text = []
    current_chunk = ""
    for chunk_text in raw_chunks:
        test_chunk = current_chunk + chunk_text if current_chunk else chunk_text
        test_chunk_token_len = token_len(test_chunk)

        crop_test_chunk = test_chunk.replace("\n", "").replace("'", "")
        crop_test_chunk = crop_test_chunk[:100]

        if not current_chunk or test_chunk_token_len <= max_chunk_size:
            current_chunk = test_chunk
        elif token_len(current_chunk) < min_chunk_size:
            current_chunk = test_chunk
        else:
            merged_chunks_text.append(current_chunk)
            current_chunk = chunk_text

    if current_chunk:
        merged_chunks_text.append(current_chunk)

    # print (f"////////////merged_chunks_text LEN:{len(merged_chunks_text)}")

    final_chunks = []
    for i, chunk_text in enumerate(merged_chunks_text):
        # Combine document-level metadata with chunk-specific metadata
        combined_metadata = (document_metadata or {}).copy()
        combined_metadata.update({
            "document_id": document_id,
            "chunk_number": i,
        })
        
        final_chunks.append({
            "chunk_id": f"{document_id}_{i}", # Create a more unique ID
            "text": chunk_text.strip(),
            "metadata": combined_metadata
        })

        # chunk_text_tokenlen = token_len(chunk_text)
        # print (f"------------------->>>chunk_text_tokenlen:{chunk_text_tokenlen}")

    return final_chunks
    
def ingest(document_paths: List[str]) -> None:
    
    db_path = "./lancedb"
    lancedb_manager = LanceDBManager(db_path=db_path)
    vector_indexer = VectorIndexer(lancedb_manager)

    llm_client = OllamaClient()

    # ov_path = "././overviews/overviews.jsonl"
    # overview_builder = OverviewBuilder(
    #     llm_client=llm_client,
    #     model="qwen3:0.6b",
    #     first_n_chunks=5,
    #     out_path=ov_path
    # )
    
    table_name = "text_pages_v3"
    lc_table_name = "text_pages_v3_lc"
    latechunk_encoder = LateChunkEncoder(model_name="qwen3-embedding-0.6b")

    page_all_chunks = []
    doc_chunks_map = {}

    login("*************************************")
    print ("hf login done")

    for path in document_paths:
        doc_id = os.path.basename(path)
        doc_metadata, sections = parse_markdown(path)
        print(f"Processing {doc_id} len_sections:{len(sections)}")
        file_chunks = []
        for isec, sec in enumerate(sections):
            text_content = sec["content"]
            page_metadata = sec["page_metadata"]
            heading = sec["heading"]
            page_number = sec["page_number"]
            chunks = chunk(text_content, doc_id, page_metadata)

            chunk_index = 0
            for c in chunks:
                text = c["text"]
                chunk_id = str(isec) + str(page_number) + str(chunk_index)

                print (f">>>>{doc_id}/{isec}-{page_number}-{chunk_index}")

                window_size=1
                local_context_text = create_contextual_window(chunks, chunk_index=chunk_index, window_size=window_size)

                # summary = generate_summary(local_context_text, text)

                token_length = token_len(text)

                meta = {
                    "doc_id": doc_id,
                    "heading": heading,
                    "text": text,
                    "token_length": token_length,
                    # "contextual_summary": summary,
                    "doc_metadata": doc_metadata,
                    "page_metadata": page_metadata,
                }

                file_chunk = {"text": text, "metadata":meta, "chunk_id": chunk_id}
                page_all_chunks.append(file_chunk)
                file_chunks.append(file_chunk)

                chunk_index += 1
        
        doc_chunks_map[doc_id] = file_chunks
        
        # print (">>>>>Building overview")
        # overview_builder.build_and_store(doc_id, file_chunks)
        # print (">>>>>Overview built")

    # embeddings
    print (">>>>>Generating embeddings")
    qwen_embedder = QwenEmbedder("Qwen/Qwen3-Embedding-0.6B")
    embedding_generator = EmbeddingGenerator(embedding_model=qwen_embedder)
    embeddings = embedding_generator.generate(page_all_chunks)      

    if len(page_all_chunks) != len(embeddings):
        print (f"The number of chunks and embeddings must be the same len(page_all_chunks):{len(page_all_chunks)} len(embeddings):{len(embeddings)}")
    else:
        vector_indexer.index(table_name, page_all_chunks, embeddings)
    print (">>>>>Embeddings generated")

    # B25
    print (">>>>>Creating FTS index")
    try:
        tbl = lancedb_manager.get_table(table_name)
        existing_indices = [idx.name for idx in tbl.list_indices()]
        if not any(name in existing_indices for name in ("text_idx", "fts_text")):
            tbl.create_fts_index(
                "text",
                use_tantivy=False,
                replace=False,
            )
            print("FTS index created successfully (using Lance native FTS).")
        else:
            print("FTS index already exists – skipped creation.")
    except Exception as e:
        print(f"Failed to create/verify FTS index: {e}")
    print (">>>>>FTS index created")
        
    # late chunking
    print (">>>>>Creating late chunking table")
    total_lc_vecs = 0
    for doc_id, doc_chunks in doc_chunks_map.items():
        full_text_parts = []
        spans = []
        current_pos = 0
        for ch in doc_chunks:
            ch_text = ch["text"]
            full_text_parts.append(ch_text)
            start = current_pos
            end = start + len(ch_text)
            spans.append((start, end))
            current_pos = end + 1  # +1 for newline to join later
        full_doc = "\n".join(full_text_parts)

        try:
            lc_vecs = latechunk_encoder.encode(full_doc, spans)
        except Exception as e:
            print(f"LateChunk encode failed for {doc_id}: {e}")
            continue

        if len(doc_chunks) == 0 or len(lc_vecs) == 0:
            # Nothing to index for this document
            continue
        if len(lc_vecs) != len(doc_chunks):
            print(f"Mismatch LC vecs ({len(lc_vecs)}) vs chunks ({len(doc_chunks)}) for {doc_id}. Skipping.")
            continue

        vector_indexer.index(lc_table_name, doc_chunks, lc_vecs)
        total_lc_vecs += len(lc_vecs)        
    print (">>>>>Late chunking table created")

    # print (">>>>>Extracting graph")
    # graph_path = "./graph/default_graph.gml"
    # graph_extractor = GraphExtractor(llm_client=llm_client,llm_model="qwen3:0.6b")
    # graph_data = graph_extractor.extract(page_all_chunks)
    # G = nx.DiGraph()
    # for entity in graph_data['entities']:
    #     G.add_node(entity['id'], type=entity.get('type', 'Unknown'), properties=entity.get('properties', {}))
    # for rel in graph_data['relationships']:
    #     G.add_edge(rel['source'], rel['target'], label=rel['label'])
    
    # os.makedirs(os.path.dirname(graph_path), exist_ok=True)
    # nx.write_gml(G, graph_path)
    # print (">>>>>Graph extracted")
    
    print ("end!")