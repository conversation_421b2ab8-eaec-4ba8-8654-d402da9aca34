from typing import List, Dict, Union, Any
import json
import pandas as pd
import numpy as np
from .embedder import <PERSON><PERSON><PERSON><PERSON><PERSON>, Embedding<PERSON>enerator, LanceDBManager, VectorIndexer
from .ollama import OllamaClient
from .retrievers import MultiVectorRetriever
from .rerank import <PERSON><PERSON><PERSON><PERSON>ker
import lancedb
import torch
import math
from transformers import AutoModel, AutoTokenizer
from threading import Lock

table_name = "text_pages_v3"
_ai_reranker_init_lock: Lock = Lock()
_rerank_lock: Lock = Lock()

def synthesize_final_answer(query: str, facts: str, *, event_callback=None) -> str:
    prompt = f"""
You are an AI assistant specialised in answering questions from retrieved context.

Context you receive
• VERIFIED FACTS – text snippets retrieved from the user's documents. Some may be irrelevant noise.  
• ORIGINAL QUESTION – the user's actual query.

Instructions
1. Evaluate each snippet for relevance to the ORIGINAL QUESTION; ignore those that do not help answer it.  
2. Synthesise an answer **using only information from the relevant snippets**.  
3. If snippets contradict one another, mention the contradiction explicitly.  
4. If the snippets do not contain the needed information, reply exactly with:  
"I could not find that information in the provided documents."  
5. Provide a thorough, well-structured answer. Use paragraphs or bullet points where helpful, and include any relevant numbers/names exactly as they appear. There is **no strict sentence limit**, but aim for clarity over brevity.  
6. Do **not** introduce external knowledge unless step 4 applies; in that case you may add a clearly-labelled "General knowledge" sentence after the required statement.

Output format
Answer:
<your answer here>

–––––  Retrieved Snippets  –––––
{facts}
––––––––––––––––––––––––––––––

ORIGINAL QUESTION: "{query}"
"""
    
    # print("\n===>>prompt<<===")
    # print(f"\n{prompt}")

    ollama_client = OllamaClient()
    answer_parts: list[str] = []
    for tok in ollama_client.stream_completion(
        model="qwen3:8b",
        prompt=prompt,
    ):
        answer_parts.append(tok)
        if event_callback:
            event_callback("token", {"text": tok})

    return "".join(answer_parts)

def get_ai_reranker():
    ai_reranker = None
    try:
        model_name = "answerdotai/answerai-colbert-small-v1"
        print(f"Initialising Answer.AI ColBERT reranker ({model_name}) via rerankers lib…")
        from rerankers import Reranker
        ai_reranker = Reranker(model_name, model_type="colbert")

        print("AI reranker initialized successfully.")
    except Exception as e:
        print(f"Failed to initialize AI reranker: {e}")

    return ai_reranker

def get_surrounding_chunks_lancedb(chunk: Dict[str, Any], window_size: int) -> List[Dict[str, Any]]:
    db_path = "./lancedb"
    db_manager = LanceDBManager(db_path=db_path)  

    if not db_manager:
        return [chunk]

    # Extract identifiers needed for the query
    document_id = chunk.get("document_id")
    chunk_index = chunk.get("chunk_index")

    # If essential identifiers are missing, return the chunk itself
    if document_id is None or chunk_index is None or chunk_index == -1:
        return [chunk]

    table_name = "text_pages_v3"
    try:
        tbl = db_manager.get_table(table_name)
    except Exception:
        # If the table can't be opened, we can't get surrounding chunks
        return [chunk]

    # Define the window for the search
    start_index = max(0, chunk_index - window_size)
    end_index = chunk_index + window_size
    
    # Construct the SQL filter for an efficient metadata-based search
    sql_filter = f"document_id = '{document_id}' AND chunk_index >= {start_index} AND chunk_index <= {end_index}"
    
    try:
        # Execute a filter-only search, which is very fast on indexed metadata
        results = tbl.search().where(sql_filter).to_list()
        
        # The results must be sorted by chunk_index to maintain logical order
        results.sort(key=lambda c: c['chunk_index'])

        # The 'metadata' field is a JSON string and needs to be parsed
        for res in results:
            if isinstance(res.get('metadata'), str):
                try:
                    res['metadata'] = json.loads(res['metadata'])
                except json.JSONDecodeError:
                    res['metadata'] = {} # Handle corrupted metadata gracefully
        return results
    except Exception:
        # If the query fails for any reason, fall back to the single chunk
        return [chunk]

def get_dense_retriever(db_manager):
    try:
        text_embedder = QwenEmbedder(model_name="Qwen/Qwen3-Embedding-0.6B")
        dense_retriever = MultiVectorRetriever(
            db_manager,
            text_embedder
        )
    except Exception as e:
        print(f"Failed to initialise dense retriever: {e}")
        dense_retriever = None

    return dense_retriever

def run_create_embeddings(texts: List[str]) -> np.ndarray:
    if torch.cuda.is_available():
        device = "cuda"
    elif getattr(torch.backends, "mps", None) and torch.backends.mps.is_available():
        device = "mps"
    else:
        device = "cpu"

    model_name = "Qwen/Qwen3-Embedding-0.6B"

    tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True, padding_side="left")
    model = AutoModel.from_pretrained(
        model_name,
        trust_remote_code=True,
        torch_dtype=torch.float16 if device != "cpu" else None,
    ).to(device).eval()
    
    inputs = tokenizer(texts, padding=True, truncation=True, return_tensors="pt").to(device)
    with torch.no_grad():
        outputs = model(**inputs)
        last_hidden = outputs.last_hidden_state  # [B, seq, dim]
        seq_len = inputs["attention_mask"].sum(dim=1) - 1  # index of last token
        batch_indices = torch.arange(last_hidden.size(0), device=device)
        embeddings = last_hidden[batch_indices, seq_len]
    
    embeddings_np = embeddings.cpu().numpy()
    
    if np.isnan(embeddings_np).any():
        print(f"Warning: NaN values detected in embeddings from {self.model_name}")
        embeddings_np = np.nan_to_num(embeddings_np, nan=0.0, posinf=0.0, neginf=0.0)
        print(f"Replaced NaN values with zeros")
    
    if np.isinf(embeddings_np).any():
        print(f"Warning: Infinite values detected in embeddings from {self.model_name}")
        embeddings_np = np.nan_to_num(embeddings_np, nan=0.0, posinf=0.0, neginf=0.0)
        print(f"Replaced infinite values with zeros")
    
    return embeddings_np
    
def run_fts_future(text_query,tbl,k):
    fts_query = text_query
    if len(text_query.split()) == 1:
        fts_query = f"{text_query}* OR {text_query}~"

    fts_k = k // 2

    return (
            tbl.search(query=fts_query, query_type="fts")
            .limit(fts_k)
            .to_df()
        )

def run_vec_future(text_query_embedding,tbl,k):
    fts_k = k // 2
    vec_k = k - fts_k

    if vec_k == 0:
        return None
    return (
        tbl.search(text_query_embedding)
            .limit(vec_k * 2)  # fetch extra to allow for dedup
            .to_df()
    )
        
def query(questions: List[str]) -> List[str]:
    # text_query_embedding = create_embeddings(questions)

    # retrieved_docs = []
    # if dense_retriever:
    #     retrieved_docs = dense_retriever.retrieve(
    #         text_query=text_query_embedding,
    #         table_name=table_name,
    #         k=10,
    #         reranker=lancedb_reranker
    #     )
    # print (retrieved_docs)

    create_embeddings = run_create_embeddings(questions)
    text_query_embedding = create_embeddings[0]

    k = 10
    table_name = "text_pages_v3"
    db_path = "./lancedb"
    db_manager = LanceDBManager(db_path=db_path)    
    tbl = db_manager.get_table(table_name)

    # print ("++++++++++Table schema:")
    # print(tbl.schema)
    # print ("/////////")
    # print(tbl.list_indices()) 
    # print ("----------Table schema:")

    fts_df = run_fts_future(questions[0],tbl, k)
    vec_df = run_vec_future(text_query_embedding,tbl, k)

    if vec_df is not None:
        combined = pd.concat([fts_df, vec_df])
    else:
        combined = fts_df

    dedup_subset = ["_rowid"] if "_rowid" in combined.columns else (["chunk_id"] if "chunk_id" in combined.columns else None)
    if dedup_subset:
        combined = combined.drop_duplicates(subset=dedup_subset, keep="first")
    combined = combined.head(k)

    results_df = combined

    retrieved_docs = []
    for _, row in results_df.iterrows():    
        metadata = json.loads(row.get('metadata', '{}'))
        # Add top-level fields back into metadata for consistency if they don't exist
        metadata.setdefault('document_id', row.get('document_id'))
        metadata.setdefault('chunk_index', row.get('chunk_index'))
        
        # Determine score (vector distance or FTS). Replace NaN with 0.0
        raw_score = row.get('_distance') if '_distance' in row else row.get('score')
        try:
            if raw_score is None or (isinstance(raw_score, float) and math.isnan(raw_score)):
                raw_score = 0.0
        except Exception:
            raw_score = 0.0

        combined_score = raw_score
        # Optional linear-weight fusion if both FTS & vector scores exist
        if '_distance' in row and 'score' in row:
            try:
                bm25 = row.get('score', 0.0)
                vec_sim = 1.0 / (1.0 + row.get('_distance', 1.0))  # convert distance to similarity
                w_bm25 = float(0.5)
                w_vec = float(0.5)
                combined_score = w_bm25 * bm25 + w_vec * vec_sim
            except Exception:
                pass

        retrieved_docs.append({
            'chunk_id': row.get('chunk_id'),
            'text': metadata.get('original_text', row.get('text')),
            'score': combined_score,
            'bm25': row.get('score'),
            '_distance': row.get('_distance'),
            'document_id': row.get('document_id'),
            'chunk_index': row.get('chunk_index'),
            'metadata': metadata
        })

    for i, d in enumerate(retrieved_docs[:10]):
        snippet = (d.get('text','') or '')[:200].replace('\n',' ')
        print(f"Orig[{i}] id={d.get('chunk_id')} dist={d.get('_distance','') or d.get('score','')}  {snippet}")
    
    dense_retriever = get_dense_retriever(db_manager)
    print (dense_retriever)

    rerank_weight = 0.5
    lancedb_reranker = lancedb.rerankers.LinearCombinationReranker(weight=rerank_weight)

    s2_retrieved_docs = []
    try:
        if dense_retriever:
            s2_retrieved_docs = dense_retriever.retrieve(
                text_query=questions[0],
                table_name=table_name,
                k=10,
                reranker=lancedb_reranker
            )
        # print ("s2_retrieved_docs\n",s2_retrieved_docs)    
    except Exception as e:
        print(f"Failed to s2_retrieved_docs: {e}")


    # Late-Chunk retrieval
    lc_table = "text_pages_v3_lc"
    try:
        lc_docs = dense_retriever.retrieve(
            text_query=questions[0],
            table_name=lc_table,
            k=10,
            reranker=lancedb_reranker,
        )    
        s2_retrieved_docs.extend(lc_docs)
        # print ("lc_docs\n",lc_docs)    
    except Exception as e:
        print(f"Failed to lc_docs: {e}")            

    # Late-Chunk merging
    merged_count = 0
    for doc in s2_retrieved_docs:
        try:
            cid = doc.get("chunk_id")
            meta = doc.get("metadata", {})
            if meta.get("latechunk_merged"):
                continue  # already processed
            doc_id = doc.get("document_id")
            cidx = doc.get("chunk_index")
            if doc_id is None or cidx is None or cidx == -1:
                continue
            # Fetch neighbouring late-chunks inside same document (±1)
            siblings = get_surrounding_chunks_lancedb(doc, window_size=1)
            # Keep only same document_id and ordered by chunk_index
            siblings = [s for s in siblings if s.get("document_id") == doc_id]
            siblings.sort(key=lambda s: s.get("chunk_index", 0))
            merged_text = " \n".join(s.get("text", "") for s in siblings)
            if merged_text:
                doc["text"] = merged_text
                meta["latechunk_merged"] = True
                merged_count += 1
        except Exception as e:
            print(f"Late-chunk merge failed for chunk {doc.get('chunk_id')}: {e}")
    if merged_count:
        print(f"🪄 Late-chunk merging applied to {merged_count} retrieved chunks.")

    # ai rerank
    ai_reranker = get_ai_reranker()
    print (ai_reranker)

    top_k_cfg = 10
    top_percent = 0.5
    
    try:
        pct = float(top_percent)
        assert 0 < pct <= 1
        top_k = max(1, int(len(s2_retrieved_docs) * pct))
    except Exception:
        print("Invalid top_percent value; falling back to top_k")
        top_k = top_k_cfg or len(s2_retrieved_docs)

    texts = [d['text'] for d in retrieved_docs]
    with _rerank_lock:
        print ("======questions[0]:")
        print (questions[0])
        ranked = ai_reranker.rank(query=questions[0], docs=texts)

    try:
        pairs = [(r.score, r.document.doc_id) for r in ranked.results]
        if any(p[1] is None for p in pairs):
            pairs = [(r.score, i) for i, r in enumerate(ranked.results)]
    except Exception:
        pairs = ranked

    if top_k is not None and len(pairs) > top_k:
        pairs = pairs[:top_k]
    
    # reranked_docs = [retrieved_docs[idx] | {"rerank_score": score} for score, idx in pairs]
    final_docs = [retrieved_docs[idx] | {"rerank_score": score} for score, idx in pairs]
    # print (final_docs)

    if not final_docs:
        return {"answer": "I could not find an answer in the documents."}
       
    # sanitize docs for JSON
    def _clean_val(v):
        if isinstance(v, float) and (math.isnan(v) or math.isinf(v)):
            return None
        if isinstance(v, (np.floating,)):
            try:
                f = float(v)
                if math.isnan(f) or math.isinf(f):
                    return None
                return f
            except Exception:
                return None
        return v

    for doc in final_docs:
        doc.pop("vector", None)
        doc.pop("_distance", None)
        for key in ['score', '_distance', 'rerank_score']:
            if key in doc:
                doc[key] = _clean_val(doc[key])

    context = "\n\n".join([doc['text'] for doc in final_docs])

    # print("\n=== Context passed to LLM (post-pruning) ===")
    # if len(context) > 2000:
    #     print(context[:2000] + "…\n[truncated] (total {} chars)".format(len(context)))
    # else:
    #     print(context)
    # print("=== End of context ===\n")

    final_answer = synthesize_final_answer(questions[0], context, event_callback=None)
    
    # return {"answer": final_answer, "source_documents": final_docs}
    return {"answer": final_answer}
        