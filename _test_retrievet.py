#!/usr/bin/env python3

import rag_pipeline.query as query

import glob
import os
import json

if __name__ == "__main__":

    resources_dir_name = "qa"
    project_root = os.path.dirname(__file__)
    resources_path = os.path.join(project_root, resources_dir_name)
    sample_answers_file = glob.glob(os.path.join(resources_path, "sample_answers.json"))    

    with open(sample_answers_file, "r", encoding="utf-8") as file:
        data = json.load(file)

    questions = [item["question"] for item in data]
    # questions = ["What is the maximum duration of a commercial lease?"]
    # answers = query.query(questions)
    # print (answers["answer"])